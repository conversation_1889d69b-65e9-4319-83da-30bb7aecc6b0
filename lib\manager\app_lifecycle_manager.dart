import 'dart:async';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/cache/cache_manager.dart';

/// 应用生命周期管理器
/// 
/// 负责监听应用生命周期变化，在应用关闭时执行必要的清理工作
class AppLifecycleManager extends GetxService with WidgetsBindingObserver {
  /// 单例实例
  static AppLifecycleManager? _instance;
  
  /// 是否已经执行过清理
  bool _hasCleanedUp = false;
  
  /// 私有构造函数
  AppLifecycleManager._();
  
  /// 获取单例实例
  static AppLifecycleManager getInstance() {
    _instance ??= AppLifecycleManager._();
    return _instance!;
  }
  
  /// 初始化并注册为GetX服务
  static void init() {
    final instance = getInstance();
    
    // 注册为GetX服务
    if (!Get.isRegistered<AppLifecycleManager>()) {
      Get.put<AppLifecycleManager>(instance, permanent: true);
    }
    
    // 注册应用生命周期监听
    WidgetsBinding.instance.addObserver(instance);
    
    LogUtil.debug('AppLifecycleManager 初始化完成');
  }
  
  /// 从GetX获取单例
  static AppLifecycleManager get to => Get.find<AppLifecycleManager>();
  
  @override
  void onInit() {
    super.onInit();
    LogUtil.debug('AppLifecycleManager服务初始化完成');
  }
  
  @override
  void onClose() {
    // 移除应用生命周期监听
    WidgetsBinding.instance.removeObserver(this);
    super.onClose();
    LogUtil.debug('AppLifecycleManager服务已关闭');
  }
  
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    LogUtil.debug('应用生命周期状态变化: $state');
    
    switch (state) {
      case AppLifecycleState.detached:
        // 应用即将被销毁，执行清理工作
        _performCleanup();
        break;
      case AppLifecycleState.paused:
        // 应用进入后台，可以执行一些轻量级清理
        _performLightCleanup();
        break;
      case AppLifecycleState.resumed:
        // 应用恢复前台
        LogUtil.debug('应用恢复前台');
        break;
      case AppLifecycleState.inactive:
        // 应用不活跃状态
        LogUtil.debug('应用进入不活跃状态');
        break;
      case AppLifecycleState.hidden:
        // 应用被隐藏
        LogUtil.debug('应用被隐藏');
        break;
    }
  }
  
  /// 执行完整清理工作
  void _performCleanup() {
    if (_hasCleanedUp) {
      LogUtil.debug('清理工作已执行过，跳过重复清理');
      return;
    }
    
    _hasCleanedUp = true;
    LogUtil.info('应用即将销毁，开始执行清理工作...');
    
    try {
      // 清理CacheManager资源
      if (Get.isRegistered<CacheManager>()) {
        final cacheManager = Get.find<CacheManager>();
        cacheManager.dispose();
        LogUtil.debug('CacheManager资源已清理');
      }
      
      LogUtil.info('应用清理工作完成');
    } catch (e) {
      LogUtil.error('应用清理工作失败: $e');
    }
  }
  
  /// 执行轻量级清理工作
  void _performLightCleanup() {
    try {
      LogUtil.debug('应用进入后台，执行轻量级清理');
      
      // 可以在这里添加一些轻量级的清理工作
      // 比如清理临时缓存、暂停非必要的定时器等
      
    } catch (e) {
      LogUtil.error('轻量级清理失败: $e');
    }
  }
  
  /// 手动触发清理（用于测试或特殊情况）
  void forceCleanup() {
    LogUtil.info('手动触发应用清理工作');
    _performCleanup();
  }
}
