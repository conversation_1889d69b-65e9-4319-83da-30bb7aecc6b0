import 'dart:async';
import 'package:get/get.dart';
import 'package:rolio/common/cache/memory_cache.dart';
import 'package:rolio/common/cache/persistent_cache.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/constants/cache_constants.dart';

/// 缓存策略枚举
enum CacheStrategy {
  /// 仅内存缓存
  memoryOnly,
  
  /// 仅持久化缓存
  persistentOnly,
  
  /// 先查内存后查持久化
  memoryThenPersistent,
  
  /// 双重写入，先读内存再读持久化
  both
}

/// 缓存管理器接口
abstract class ICacheManager {
  /// 从缓存获取数据
  /// 
  /// [key] - 缓存键
  /// [type] - 指定返回的数据类型
  /// [fromJson] - 将JSON转换为对象的函数
  /// [strategy] - 缓存策略，默认为先查内存后查持久化
  /// [maxAge] - 缓存有效期，单位为毫秒
  FutureOr<T?> get<T>(
    String key, {
    CacheStrategy strategy = CacheStrategy.memoryThenPersistent,
    int? maxAge,
    T? Function(Map<String, dynamic>)? fromJson,
  });

  /// 设置缓存数据
  /// 
  /// [key] - 缓存键
  /// [data] - 要缓存的数据
  /// [strategy] - 缓存策略，默认为同时写入内存和持久化
  /// [expiry] - 过期时间，单位为毫秒
  FutureOr<bool> set<T>(
    String key, 
    T data, {
    CacheStrategy strategy = CacheStrategy.both,
    int? expiry,
    Map<String, dynamic> Function(T)? toJson,
  });

  /// 删除缓存
  /// 
  /// [key] - 要删除的缓存键
  /// [strategy] - 缓存策略，默认为同时删除内存和持久化缓存
  FutureOr<bool> remove(
    String key, {
    CacheStrategy strategy = CacheStrategy.both,
  });

  /// 清除所有缓存
  /// 
  /// [strategy] - 缓存策略，默认为同时清除内存和持久化缓存
  FutureOr<bool> clear({
    CacheStrategy strategy = CacheStrategy.both,
  });

  /// 检查缓存是否存在
  /// 
  /// [key] - 缓存键
  /// [strategy] - 缓存策略，默认为先查内存后查持久化
  FutureOr<bool> exists(
    String key, {
    CacheStrategy strategy = CacheStrategy.memoryThenPersistent,
  });

  /// 获取缓存过期时间
  /// 
  /// [key] - 缓存键
  /// 返回缓存过期时间的毫秒时间戳，如果缓存不存在或已过期则返回null
  FutureOr<int?> getExpiryTime(String key);

  /// 获取缓存剩余有效时间（毫秒）
  /// 
  /// [key] - 缓存键
  /// 返回缓存剩余有效时间（毫秒），如果缓存不存在或已过期则返回0
  FutureOr<int> getRemainingTime(String key);
  
  /// 获取所有缓存键
  /// 
  /// [strategy] - 缓存策略，默认为同时获取内存和持久化缓存的键
  /// 返回所有缓存键的列表
  FutureOr<List<String>> getKeys({
    CacheStrategy strategy = CacheStrategy.both,
  });
}

/// 缓存管理器实现
/// 
/// 统一管理内存缓存和持久化缓存，提供灵活的缓存策略
class CacheManager implements ICacheManager {
  /// 单例实例
  static CacheManager? _instance;

  /// 内存缓存
  final MemoryCache _memoryCache;
  
  /// 持久化缓存
  final PersistentCache _persistentCache;
  
  /// 默认缓存有效期（毫秒）
  final int defaultExpiry;

  /// 写入锁，防止并发写入冲突
  final Map<String, Completer<bool>> _writeLocks = {};

  /// 锁超时时间（毫秒）
  static const int _lockTimeoutMs = 30000; // 30秒超时

  /// 最大锁数量限制
  static const int _maxLocks = 1000;

  /// 缓存错误统计
  int _cacheErrors = 0;

  /// 私有构造函数
  CacheManager._({
    MemoryCache? memoryCache,
    PersistentCache? persistentCache,
    int? defaultExpiry,
  }) : 
    _memoryCache = memoryCache ?? MemoryCache(),
    _persistentCache = persistentCache ?? PersistentCache(),
    defaultExpiry = defaultExpiry ?? CacheConstants.getPersistentExpiryMs();
  
  /// 获取单例实例
  static CacheManager getInstance() {
    _instance ??= CacheManager._();
    return _instance!;
  }
  
  /// 注册为GetX服务
  static Future<void> init() async {
    final instance = getInstance();
    await instance._persistentCache.init();
    
    // 注册为GetX服务
    if (!Get.isRegistered<CacheManager>()) {
      Get.put<CacheManager>(instance, permanent: true);
    }
    
    LogUtil.debug('CacheManager 初始化完成');
  }
  
  /// 从GetX获取单例
  static CacheManager get to => Get.find<CacheManager>();
  
  @override
  Future<T?> get<T>(
    String key, {
    CacheStrategy strategy = CacheStrategy.memoryThenPersistent,
    int? maxAge,
    T? Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      switch (strategy) {
        case CacheStrategy.memoryOnly:
          return _memoryCache.get<T>(key, maxAge: maxAge, fromJson: fromJson);
          
        case CacheStrategy.persistentOnly:
          return await _persistentCache.get<T>(key, maxAge: maxAge, fromJson: fromJson);
          
        case CacheStrategy.memoryThenPersistent:
          final memData = _memoryCache.get<T>(key, maxAge: maxAge, fromJson: fromJson);
          if (memData != null) {
            return memData;
          }
          final diskData = await _persistentCache.get<T>(key, maxAge: maxAge, fromJson: fromJson);
          if (diskData != null) {
            // 写入内存缓存，以便下次快速访问
            await _memoryCache.set(key, diskData, expiry: maxAge ?? defaultExpiry);
          }
          return diskData;
          
        case CacheStrategy.both:
          final memData = _memoryCache.get<T>(key, maxAge: maxAge, fromJson: fromJson);
          if (memData != null) {
            return memData;
          }
          return await _persistentCache.get<T>(key, maxAge: maxAge, fromJson: fromJson);
      }
    } catch (e) {
      _cacheErrors++;
      LogUtil.error('缓存获取失败: $key, 错误: $e (总错误数: $_cacheErrors)');
      return null;
    }
  }

  @override
  Future<bool> set<T>(
    String key,
    T data, {
    CacheStrategy strategy = CacheStrategy.both,
    int? expiry,
    Map<String, dynamic> Function(T)? toJson,
  }) async {
    // 检查锁数量限制，防止内存泄漏
    if (_writeLocks.length >= _maxLocks) {
      LogUtil.warn('缓存写入锁数量达到上限($_maxLocks)，清理过期锁');
      _cleanupExpiredLocks();

      // 如果清理后仍然超限，拒绝新的写入请求
      if (_writeLocks.length >= _maxLocks) {
        LogUtil.error('缓存写入锁数量仍然超限，拒绝写入: $key');
        return false;
      }
    }

    // 防止并发写入同一个key，添加超时机制
    if (_writeLocks.containsKey(key)) {
      try {
        await _writeLocks[key]!.future.timeout(
          const Duration(milliseconds: _lockTimeoutMs),
          onTimeout: () {
            LogUtil.warn('等待写入锁超时: $key');
            return false;
          },
        );
      } catch (e) {
        LogUtil.error('等待写入锁异常: $key, 错误: $e');
        // 清理可能损坏的锁
        _writeLocks.remove(key);
      }
    }

    final completer = Completer<bool>();
    _writeLocks[key] = completer;

    try {
      final effectiveExpiry = expiry ?? defaultExpiry;

      bool result;
      switch (strategy) {
        case CacheStrategy.memoryOnly:
          result = await _memoryCache.set(key, data, expiry: effectiveExpiry, toJson: toJson);
          break;

        case CacheStrategy.persistentOnly:
          result = await _persistentCache.set(key, data, expiry: effectiveExpiry, toJson: toJson);
          break;

        case CacheStrategy.memoryThenPersistent:
        case CacheStrategy.both:
          final memResult = await _memoryCache.set(key, data, expiry: effectiveExpiry, toJson: toJson);
          final diskResult = await _persistentCache.set(key, data, expiry: effectiveExpiry, toJson: toJson);
          result = memResult && diskResult;
          break;
      }

      // 确保completer未被完成才调用complete
      if (!completer.isCompleted) {
        completer.complete(result);
      }
      return result;
    } catch (e) {
      _cacheErrors++;
      LogUtil.error('缓存设置失败: $key, 错误: $e (总错误数: $_cacheErrors)');

      // 确保completer未被完成才调用complete
      if (!completer.isCompleted) {
        completer.complete(false);
      }
      return false;
    } finally {
      // 安全移除锁，即使在异常情况下也能执行
      _writeLocks.remove(key);
    }
  }

  @override
  Future<bool> remove(
    String key, {
    CacheStrategy strategy = CacheStrategy.both,
  }) async {
    try {
      switch (strategy) {
        case CacheStrategy.memoryOnly:
          return _memoryCache.remove(key);
          
        case CacheStrategy.persistentOnly:
          return await _persistentCache.remove(key);
          
        case CacheStrategy.memoryThenPersistent:
        case CacheStrategy.both:
          final memResult = _memoryCache.remove(key);
          final diskResult = await _persistentCache.remove(key);
          return memResult && diskResult;
      }
    } catch (e) {
      LogUtil.error('缓存删除失败: $key, 错误: $e');
      return false;
    }
  }

  @override
  Future<bool> clear({
    CacheStrategy strategy = CacheStrategy.both,
  }) async {
    try {
      switch (strategy) {
        case CacheStrategy.memoryOnly:
          return _memoryCache.clear();
          
        case CacheStrategy.persistentOnly:
          return await _persistentCache.clear();
          
        case CacheStrategy.memoryThenPersistent:
        case CacheStrategy.both:
          final memResult = _memoryCache.clear();
          final diskResult = await _persistentCache.clear();
          return memResult && diskResult;
      }
    } catch (e) {
      LogUtil.error('缓存清除失败: 错误: $e');
      return false;
    }
  }

  @override
  Future<bool> exists(
    String key, {
    CacheStrategy strategy = CacheStrategy.memoryThenPersistent,
  }) async {
    try {
      switch (strategy) {
        case CacheStrategy.memoryOnly:
          return _memoryCache.exists(key);
          
        case CacheStrategy.persistentOnly:
          return await _persistentCache.exists(key);
          
        case CacheStrategy.memoryThenPersistent:
          if (_memoryCache.exists(key)) {
            return true;
          }
          return await _persistentCache.exists(key);
          
        case CacheStrategy.both:
          final memExists = _memoryCache.exists(key);
          final diskExists = await _persistentCache.exists(key);
          return memExists || diskExists;
      }
    } catch (e) {
      LogUtil.error('缓存检查失败: $key, 错误: $e');
      return false;
    }
  }

  @override
  Future<int?> getExpiryTime(String key) async {
    // 优先检查内存缓存
    final memExpiry = _memoryCache.getExpiryTime(key);
    if (memExpiry != null) {
      return memExpiry;
    }
    // 如果内存中不存在，检查持久化缓存
    return await _persistentCache.getExpiryTime(key);
  }

  @override
  Future<int> getRemainingTime(String key) async {
    final expiryTime = await getExpiryTime(key);
    if (expiryTime == null) {
      return 0;
    }
    
    final now = DateTime.now().millisecondsSinceEpoch;
    final remaining = expiryTime - now;
    
    return remaining > 0 ? remaining : 0;
  }
  
  @override
  Future<List<String>> getKeys({
    CacheStrategy strategy = CacheStrategy.both,
  }) async {
    try {
      final Set<String> keys = {};
      
      switch (strategy) {
        case CacheStrategy.memoryOnly:
          keys.addAll(_memoryCache.getKeys());
          break;
          
        case CacheStrategy.persistentOnly:
          keys.addAll(await _persistentCache.getKeys());
          break;
          
        case CacheStrategy.memoryThenPersistent:
        case CacheStrategy.both:
          keys.addAll(_memoryCache.getKeys());
          keys.addAll(await _persistentCache.getKeys());
          break;
      }
      
      return keys.toList();
    } catch (e) {
      LogUtil.error('获取缓存键失败: $e');
      return [];
    }
  }
  
  /// 设置默认过期时间
  void setDefaultExpiry(int milliseconds) {
    if (milliseconds > 0) {
      _memoryCache.setDefaultExpiry(milliseconds);
      _persistentCache.setDefaultExpiry(milliseconds);
    }
  }

  /// 清理过期或损坏的锁
  void _cleanupExpiredLocks() {
    try {
      final keysToRemove = <String>[];

      for (final entry in _writeLocks.entries) {
        final completer = entry.value;

        // 检查completer是否已完成或损坏
        if (completer.isCompleted) {
          keysToRemove.add(entry.key);
        }
      }

      // 移除已完成的锁
      for (final key in keysToRemove) {
        _writeLocks.remove(key);
      }

      LogUtil.debug('清理了 ${keysToRemove.length} 个过期锁，剩余锁数量: ${_writeLocks.length}');
    } catch (e) {
      LogUtil.error('清理过期锁失败: $e');
    }
  }
}