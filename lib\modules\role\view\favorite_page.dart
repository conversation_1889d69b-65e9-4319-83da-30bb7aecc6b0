import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:rolio/modules/role/controller/favorite_controller.dart';
import 'package:rolio/common/constants/colors_constants.dart';
import 'package:rolio/modules/role/view/favorite_role_card.dart';
import 'package:rolio/widgets/skeleton/shimmer_widget.dart';
import 'package:rolio/widgets/skeleton/skeleton_container.dart';
import 'package:rolio/modules/role/controller/recommend_controller.dart';
import 'package:rolio/widgets/skeleton/role_list_skeleton.dart';

/// 收藏角色页面
/// 
/// 展示用户收藏的AI角色列表，支持下拉刷新和分页加载
class FavoritePage extends GetView<FavoriteController> with WidgetsBindingObserver {
  const FavoritePage({Key? key}) : super(key: key);

  // 跟踪Observer，避免重复添加 - 修复内存泄漏
  static _PageLifecycleObserver? _observer;

  @override
  Widget build(BuildContext context) {
    // 确保页面显示时立即加载收藏角色
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.loadFavoriteRoles(forceRefresh: false);

      // 添加应用生命周期监听 - 修复内存泄漏
      if (_observer == null) {
        _observer = _PageLifecycleObserver(controller);
        WidgetsBinding.instance.addObserver(_observer!);
      }
    });
    
    // 设置系统UI样式
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarColor: Colors.black,
        systemNavigationBarIconBrightness: Brightness.light,
      ),
    );
    
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text(
          'Favorite Roles',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Get.back(),
        ),
      ),
      body: _buildBody(context),
    );
  }

  /// 构建页面主体内容
  Widget _buildBody(BuildContext context) {
    return Obx(() {
      final isLoading = controller.isLoading.value;
      final showSkeleton = controller.showSkeleton.value;
      final roles = controller.getRoles();
      
      // 如果是首次加载或强制刷新，且需要显示骨架屏
      if (showSkeleton && !controller.isLoadingMore.value) {
        return const RoleListSkeleton();
      }
      
      // 处理不同状态的UI展示
      if (isLoading && roles.isEmpty && !controller.isLoadingMore.value) {
        return _buildLoadingView();
      }
      
      if (roles.isEmpty) {
        return _buildEmptyView();
      }
      
      // 已有数据，显示角色网格
      return _buildRolesGridView(context, roles);
    });
  }
  
  /// 构建加载中视图
  Widget _buildLoadingView() {
    // 使用骨架屏替代原来的加载指示器
    return const RoleListSkeleton();
  }
  
  /// 构建空数据视图
  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.favorite_border,
            size: 76,
            color: Colors.grey.withOpacity(0.6),
          ),
          const SizedBox(height: 24),
          const Text(
            'No favorite AI characters yet',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 12),
          const Text(
            'Your favorite AI characters will appear here',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 32),
          ElevatedButton(
            onPressed: () => controller.loadFavoriteRoles(forceRefresh: true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 28, vertical: 14),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              elevation: 4,
            ),
            child: const Text(
              'Refresh',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  /// 构建角色列表视图
  Widget _buildRolesGridView(BuildContext context, List<dynamic> roles) {
    // 获取RecommendController用于启动聊天
    final recommendController = Get.find<RecommendController>();
    
    return RefreshIndicator(
      onRefresh: () => controller.refreshFavoriteRoles(),
      color: AppColors.primary,
      backgroundColor: Colors.grey[900],
      displacement: 40,
      edgeOffset: 20,
      strokeWidth: 3,
      child: NotificationListener<ScrollNotification>(
        onNotification: controller.handleScrollNotification,
        child: CustomScrollView(
          physics: const BouncingScrollPhysics(
            parent: AlwaysScrollableScrollPhysics(),
          ),
          // 使用键值保持滚动位置
          key: const PageStorageKey('favorite_list'),
          slivers: [
            // 添加顶部间距
            const SliverToBoxAdapter(
              child: SizedBox(height: 8),
            ),
            // 角色列表
            SliverPadding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              sliver: SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    if (index < roles.length) {
                      final role = roles[index];
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 12),
                        child: FavoriteRoleCard(
                          role: role,
                          onTap: () => recommendController.startChatWithRole(role),
                          onFavoriteTap: () => controller.toggleFavorite(role),
                        ),
                      );
                    } else if (controller.isLoadingMore.value) {
                      // 显示加载更多的占位符
                      return const Padding(
                        padding: EdgeInsets.only(bottom: 12),
                        child: ShimmerWidget(
                          child: SkeletonContainer(
                            width: double.infinity,
                            height: 80,
                            borderRadius: 16,
                          ),
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  },
                  // 如果正在加载更多，则多显示一个占位符
                  childCount: controller.isLoadingMore.value
                      ? roles.length + 1
                      : roles.length,
                ),
              ),
            ),
            // 底部加载更多指示器
            SliverToBoxAdapter(
              child: controller.isLoadingMore.value
                  ? Container(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      alignment: Alignment.center,
                      child: const CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                      ),
                    )
                  : const SizedBox(height: 16),
            ),
            // 底部边距
            const SliverToBoxAdapter(
              child: SizedBox(height: 32),
            ),
          ],
        ),
      ),
    );
  }
}

/// 页面生命周期观察者
class _PageLifecycleObserver extends WidgetsBindingObserver {
  final FavoriteController controller;

  _PageLifecycleObserver(this.controller);

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // 应用恢复到前台，可以刷新数据
      controller.checkForUpdates();
    }
  }

  /// 清理Observer - 修复内存泄漏
  static void cleanup() {
    if (FavoritePage._observer != null) {
      WidgetsBinding.instance.removeObserver(FavoritePage._observer!);
      FavoritePage._observer = null;
    }
  }
}