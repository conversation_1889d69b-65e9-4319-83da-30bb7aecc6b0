import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/constants/error_codes.dart';
import 'package:rolio/modules/wallet/model/wallet.dart';
import 'package:rolio/modules/wallet/model/transaction_record.dart';
import 'package:rolio/modules/wallet/service/wallet_service.dart';
import 'package:rolio/manager/global_state.dart';

/// 钱包控制器
class WalletController extends GetxController {
  /// 钱包服务
  final WalletService _walletService;
  
  /// 全局状态
  final GlobalState _globalState = Get.find<GlobalState>();

  /// Worker监听器 - 用于清理监听器
  Worker? _userStateWorker;

  /// 构造函数
  WalletController({required WalletService walletService}) 
      : _walletService = walletService;

  @override
  void onInit() {
    super.onInit();
    LogUtil.debug('WalletController: 初始化');
    
    // 监听用户状态变化
    _userStateWorker = ever(_globalState.currentUser, (user) {
      if (user != null) {
        LogUtil.debug('WalletController: 用户登录，刷新钱包信息');
        refreshWallet();
      } else {
        LogUtil.debug('WalletController: 用户登出');
      }
    });
    
    // 如果当前已有用户，立即刷新钱包信息
    if (_globalState.currentUser.value != null) {
      // 延迟一点时间确保服务已经初始化
      Future.delayed(const Duration(milliseconds: 500), () {
        refreshWallet();
      });
    }
  }

  @override
  void onClose() {
    // 清理监听器
    _userStateWorker?.dispose();
    super.onClose();
  }

  /// 当前钱包
  Wallet? get currentWallet => _walletService.currentWallet.value;

  /// 当前余额
  int get currentBalance => _walletService.currentBalance;

  /// 交易记录列表
  List<TransactionRecord> get transactions => _walletService.transactions;

  /// 是否正在加载
  bool get isLoading => _walletService.isLoading.value;

  /// 是否正在处理交易
  bool get isProcessingTransaction => _walletService.isProcessingTransaction.value;

  /// 是否已登录
  bool get isLoggedIn => _globalState.currentUser.value != null && !(_globalState.currentUser.value?.isAnonymous ?? true);

  /// 刷新钱包信息
  Future<void> refreshWallet() async {
    try {
      LogUtil.debug('WalletController: 刷新钱包信息');
      await _walletService.refreshWallet();
    } catch (e) {
      LogUtil.error('WalletController: 刷新钱包信息失败: $e');
      ErrorHandler.handleException(
        AppException('刷新钱包信息失败', originalError: e, code: ErrorCodes.BUSINESS_ERROR),
      );
    }
  }

  /// 刷新交易记录
  Future<void> refreshTransactionHistory() async {
    try {
      LogUtil.debug('WalletController: 刷新交易记录');
      await _walletService.refreshTransactionHistory(limit: 100);
    } catch (e) {
      LogUtil.error('WalletController: 刷新交易记录失败: $e');
      ErrorHandler.handleException(
        AppException('刷新交易记录失败', originalError: e, code: ErrorCodes.BUSINESS_ERROR),
      );
    }
  }

  /// 消费钻石
  Future<bool> spendDiamonds(int amount, String description) async {
    try {
      if (!isLoggedIn) {
        LogUtil.warn('WalletController: 用户未登录，无法消费钻石');
        ErrorHandler.handleException(
          AppException('请先登录', code: ErrorCodes.AUTH_ERROR),
        );
        return false;
      }

      LogUtil.debug('WalletController: 消费钻石，金额: $amount，描述: $description');
      return await _walletService.spendDiamonds(amount, description);
    } catch (e) {
      LogUtil.error('WalletController: 消费钻石失败: $e');
      ErrorHandler.handleException(
        AppException('消费失败', originalError: e, code: ErrorCodes.BUSINESS_ERROR),
      );
      return false;
    }
  }

  /// 增加钻石
  Future<bool> addDiamonds(int amount, String description, {TransactionType type = TransactionType.earn}) async {
    try {
      if (!isLoggedIn) {
        LogUtil.warn('WalletController: 用户未登录，无法增加钻石');
        ErrorHandler.handleException(
          AppException('请先登录', code: ErrorCodes.AUTH_ERROR),
        );
        return false;
      }

      LogUtil.debug('WalletController: 增加钻石，金额: $amount，描述: $description');
      return await _walletService.addDiamonds(amount, description, type: type);
    } catch (e) {
      LogUtil.error('WalletController: 增加钻石失败: $e');
      ErrorHandler.handleException(
        AppException('增加钻石失败', originalError: e, code: ErrorCodes.BUSINESS_ERROR),
      );
      return false;
    }
  }

  /// 检查是否有足够的钻石
  bool hasEnoughDiamonds(int amount) {
    return _walletService.hasEnoughDiamonds(amount);
  }

  /// 清空交易记录
  Future<void> clearTransactionHistory() async {
    try {
      LogUtil.debug('WalletController: 清空交易记录');
      
      // 显示确认对话框
      final confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: const Text('确认清空'),
          content: const Text('确定要清空所有交易记录吗？此操作不可撤销。'),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: const Text('确定'),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        final success = await _walletService.clearTransactionHistory();
        if (success) {
          ErrorHandler.showInfo('交易记录已清空');
        }
      }
    } catch (e) {
      LogUtil.error('WalletController: 清空交易记录失败: $e');
      ErrorHandler.handleException(
        AppException('清空交易记录失败', originalError: e, code: ErrorCodes.BUSINESS_ERROR),
      );
    }
  }

  /// 格式化余额显示
  String formatBalance(int balance) {
    if (balance >= 1000000) {
      return '${(balance / 1000000).toStringAsFixed(1)}M';
    } else if (balance >= 1000) {
      return '${(balance / 1000).toStringAsFixed(1)}K';
    } else {
      return balance.toString();
    }
  }

  /// 格式化交易金额显示
  String formatTransactionAmount(TransactionRecord transaction) {
    final amount = transaction.absoluteAmount;
    final sign = transaction.isIncome ? '+' : '-';
    return '$sign${formatBalance(amount)}';
  }

  /// 获取交易类型颜色
  Color getTransactionColor(TransactionRecord transaction) {
    if (transaction.isIncome) {
      return const Color(0xFF4CAF50); // 绿色
    } else {
      return const Color(0xFFF44336); // 红色
    }
  }

  /// 获取余额状态文本
  String getBalanceStatusText() {
    final balance = currentBalance;
    if (balance >= 10000) {
      return '余额充足';
    } else if (balance >= 1000) {
      return '余额正常';
    } else if (balance >= 150) {
      return '余额偏低';
    } else {
      return '余额不足';
    }
  }

  /// 获取余额状态颜色
  Color getBalanceStatusColor() {
    final balance = currentBalance;
    if (balance >= 10000) {
      return const Color(0xFF4CAF50); // 绿色
    } else if (balance >= 1000) {
      return const Color(0xFF2196F3); // 蓝色
    } else if (balance >= 150) {
      return const Color(0xFFFF9800); // 橙色
    } else {
      return const Color(0xFFF44336); // 红色
    }
  }

  /// 模拟充值（测试用）
  Future<void> simulateRecharge(int amount) async {
    try {
      LogUtil.debug('WalletController: 模拟充值，金额: $amount');
      await addDiamonds(amount, '模拟充值', type: TransactionType.earn);
    } catch (e) {
      LogUtil.error('WalletController: 模拟充值失败: $e');
    }
  }

  /// 模拟消费（测试用）
  Future<void> simulateSpend(int amount) async {
    try {
      LogUtil.debug('WalletController: 模拟消费，金额: $amount');
      await spendDiamonds(amount, '模拟消费');
    } catch (e) {
      LogUtil.error('WalletController: 模拟消费失败: $e');
    }
  }

  /// 创建测试数据
  Future<void> createTestData() async {
    try {
      LogUtil.debug('WalletController: 创建测试数据');
      await _walletService.createTestData();
      ErrorHandler.showInfo('测试数据已创建');
    } catch (e) {
      LogUtil.error('WalletController: 创建测试数据失败: $e');
      ErrorHandler.handleException(
        AppException('创建测试数据失败', originalError: e, code: ErrorCodes.BUSINESS_ERROR),
      );
    }
  }
}
