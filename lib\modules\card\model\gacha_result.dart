import 'card.dart';
import 'card_rarity.dart';

/// Gacha pull result
class GachaResult {
  final List<Card> cards;
  final int totalCost;
  final int remainingBalance;
  final DateTime timestamp;
  final String pullType;
  final Map<String, dynamic> metadata;

  const GachaResult({
    required this.cards,
    required this.totalCost,
    required this.remainingBalance,
    required this.timestamp,
    required this.pullType,
    this.metadata = const {},
  });

  /// Create gacha result from map
  factory GachaResult.fromMap(Map<String, dynamic> map) {
    return GachaResult(
      cards: (map['cards'] as List<dynamic>?)
          ?.map((cardMap) => Card.fromMap(cardMap))
          .toList() ?? [],
      totalCost: map['total_cost'] ?? 0,
      remainingBalance: map['remaining_balance'] ?? 0,
      timestamp: DateTime.tryParse(map['timestamp'] ?? '') ?? DateTime.now(),
      pullType: map['pull_type'] ?? 'single',
      metadata: Map<String, dynamic>.from(map['metadata'] ?? {}),
    );
  }

  /// Convert gacha result to map
  Map<String, dynamic> toMap() {
    return {
      'cards': cards.map((card) => card.toMap()).toList(),
      'total_cost': totalCost,
      'remaining_balance': remainingBalance,
      'timestamp': timestamp.toIso8601String(),
      'pull_type': pullType,
      'metadata': metadata,
    };
  }

  /// Get highest rarity card
  Card? get highestRarityCard {
    if (cards.isEmpty) return null;
    
    return cards.reduce((current, next) {
      return current.rarity.stars > next.rarity.stars ? current : next;
    });
  }

  /// Get cards by rarity
  List<Card> getCardsByRarity(CardRarity rarity) {
    return cards.where((card) => card.rarity == rarity).toList();
  }

  /// Check if contains legendary or mythic
  bool get hasRareCards {
    return cards.any((card) => 
      card.rarity == CardRarity.legendary || 
      card.rarity == CardRarity.mythic
    );
  }

  /// Get summary text
  String get summary {
    if (cards.isEmpty) return 'No cards obtained';
    
    final rarityCount = <CardRarity, int>{};
    for (final card in cards) {
      rarityCount[card.rarity] = (rarityCount[card.rarity] ?? 0) + 1;
    }
    
    final parts = <String>[];
    for (final entry in rarityCount.entries) {
      parts.add('${entry.value}x ${entry.key.displayName}');
    }
    
    return parts.join(', ');
  }

  @override
  String toString() {
    return 'GachaResult(cards: ${cards.length}, cost: $totalCost, type: $pullType)';
  }
}
