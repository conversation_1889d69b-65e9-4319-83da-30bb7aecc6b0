# Change Log

## [1.0.5] - 2025-07-30

### Added

- 添加角色详情页,展示角色统计数据
- 添加角色简介,增加简介气泡
- 添加角色收藏功能,支持在角色详情页收藏和在用户收藏角色列表展示
- 添加角色搜索功能,实现用户对角色的搜索
- 支持会话置顶和隐藏
- 添加加载页,优化用户体验
- 建立统一的弹窗工具
- 建立内存监控器,监控手机内存使用情况

### Fixed

- 删除不必要的接口,重构接口职责,实现统一的接口
- 修复从会话列表页进入会话详情页,左右切换用的是推荐列表的问题
- 修复删除会话找不到对应角色的问题
- 优化初始化逻辑,防止重复刷新token以及websocket的重连
- 修改错误弹窗,文本统一用英文
- 统一在role_service维护收藏状态,统一在role_service实现错误恢复机制,增加重试机制
- 增加搜索的防抖时间,防止频繁提交
- 优化角色详情缓存策略,统一分页逻辑,从第1页开始加载数据
- 为会话置顶和隐藏操作添加乐观更新的方式,添加延迟确认机制
- 统一错误处理,并在http工具中增加重试与网络状态检测
- 统一使用缓存配置文件配置缓存,增强初始化检查,添加安全文件写入机制,添加lru缓存策略
- 优化骨架屏可复用性
- 添加对TimeoutException 的专门处理,完善网络异常的细分类型处理
- 修复分页加载错误处理不完整,统一使用分页混入类
- 会话模型简化时间解析逻辑,统一时区处理方式
- 删除不必要角色详情缓存
- 弃用event_bus,使用 GetX的响应式机制来广播事件
- 重构Timer使用,改为GetX的响应式定时机制,添加响应式请求状态管理
- 使用interval() Worker替代Timer.periodic
- 使用RxBool / RxInt + 轮询等待代替Completer
- 使用Stream.periodic替代interval,更适合周期事件
- 修复角色管理状态混乱的问题,减少状态量,实现GetX状态管理优化
- 统一错误管理入口,删除无用的错误管理
- 彻底处理所有可能导致内存泄露的地方,清理缓存,释放资源
- 优化角色状态管理,使用状态模式重构,并且添加全局收藏状态,同步不同页面的收藏关系
- 统一缓存策略,使用统一的分页工具 
- 统一错误处理,添加网络重试工具
- 通过全局锁确保同一时间只能有一个置顶操作执行
- 优化搜索建议逻辑,实现搜索结果和搜索记录缓存
- 搜索统一使用枚举状态,逻辑更清晰,正确处理 hasMoreData 状态，避免无限加载
- 将GlobalFavoriteStateManager作为唯一状态源，消除状态不一致
- 修复角色提供器在获取会话列表返回空的问题
- 图片预加载器使用 WeakCallback<T> 包装回调函数，防止循环引用,改进 dispose() 方法
- 为删除所有搜索记录添加确认框

## [1.0.4] - 2025-07-23

### Added

- 接入登录功能,添加用户名和密码登录
- 修改ui样式,符合黑色主题
- 添加错误页优化网络不好的提示
- 添加加载页以及加载气泡,提高用户体验
- 添加待处理消息持久化
- 添加自动处理超时地待处理消息,重置回复状态
- 增加限制功能,现在在ai回复前用户只能发送一条消息,并添加防抖机制

### Fixed

- 修复心跳时间设置为20s,增加pong验证
- 优化封面加载速度
- 修复后台消息导致会话切换混乱的问题
- 修复websocket连接方式,修复心跳机制过于激进,增加最大重连次数
- 修复角色切换导致会话切换失败的问题,修复左右滑动切换角色时，聊天记录跟角色不对应
- 修复内存泄漏统一错误处理
- 修复打招呼语问题
- 修复url路径错误问题
- 删除过大的静态图片
- 按钮 explore 点击无反应
- 修复登陆时token设置的不正确的问题
- 修复登出时重复匿名登录地问题
- 后台消息处理不准确的问题
- 修复用户退出登录和登录时推荐页和会话页未刷新的问题
- 修复匿名用户可以改用户名的问题
- 修复名登录状态管理不一致以及Token刷新机制存在竞态条件,统一化token管理
- 修复会话绑定混乱的问题以及消息重复订阅的问题
- 修复内存泄露的问题
- 统一错误处理
- 修复登录状态持久化机制缺失以及Token存储安全性不足的问题
- 彻底解决吞消息的问题,清除websocket订阅以及重置
- 优化登录登出的刷新操作,确保推荐也和会话页刷新正确
- 修复滑动的问题,在输入框或者顶部滑动不会意外切换角色,并且增加手势检测避免误触
- 修复会话删除没有取消角色订阅以及清空消息的问题的问题,修复取消角色订阅不正确的问题
- 修复会话删除后资源清理不彻底的问题,实现原子性资源清理机制,并修复缓存清理策略过于粗暴的问题
- 修复websocket连接的问题,增加网络状态检测以及心跳超时单独计时,修复初始连接时对无效token处理不完整的问题
- 移除会话排序,交给后端负责
- 实现Token 有效期检查,添加Token刷新机制
- 修复会话缓存的问题,防止创建重复缓存
- 修改用户气泡颜色,减少心跳间隔以及ws重连间隔

## [1.0.3] - 2025-07-18

### Added

- 增加对聊天消息的举报功能

### Fixed

- 将中文界面改为英文
- 修复长按举报显示在屏幕外的问题

## [1.0.2] - 2025-07-17

### Added

- 添加举报按钮,实现举报功能

### Fixed

- 修复举报后页面错误的问题
- 修复重试逻辑可能导致多次重复提交相同的举报的问题,添加了防重复提交机制
- 修复举报时输入法展示parentDataDirty断言失败问题

## [1.0.1] - 2025-07-12

### Added

- 将原有聊天平台转换为AI聊天平台，保留原有聊天逻辑
- 实现AI角色系统，支持多种AI角色的切换和对话
- 添加角色信息展示，包括头像、封面图和角色名称
- 实现WebSocket实时通信，支持用户与AI的即时对话
- 添加会话管理功能，支持创建、切换和删除会话
- 实现历史消息加载和展示功能
- 添加AI思考状态显示，增强用户体验
- 支持角色推荐系统，用户可以发现新的AI角色
- 实现角色信息的动态加载和缓存机制
- 添加webSocket连接断开后自动重连机制和后台线程异常处理
- 添加骨架屏,优化加载状态

### Fixed

- 修复了切换AI角色后名称不更新的问题
- 修复了WebSocket服务中String.fromCharCodes()方法可能导致的非法UTF-16代理对问题
- 修复了HTTP请求中处理响应数据时可能出现的空指针异常
- 修复了推荐数据源中处理API响应时的空指针异常
- 修复了AI角色模型中处理tag_info时的空指针异常
- 修复了会话服务中处理API响应时的空指针异常
- 修复聊天后会话列表不刷新的问题
- 修复推荐页分页加载问题
- 修复时区转换问题
- 配合服务器修改请求地址
- 删除成功重连弹窗,修复无法删除会话的操作
- 修复程序启动就绑定所有模块的问题
- 使websocket和chat通过事件总线通讯,修复循环依赖问题
- 修复websocket连接,改成单连接多通道模式
- 添加加载屏优化加载体验
- 使用静默加载的方式加载会话
- 修改sender适配后端消息
- 优化一下chat的service层
- 实现事件驱动的模块间通信
- 将_silentUpdateSessionOrder逻辑移至SessionService

## [1.0.0] - 2025-07-01

### Added

- 基础页面状态 GetX管理
- 项目启动流程
- ws、http 管理
- demo 消息发送、接受