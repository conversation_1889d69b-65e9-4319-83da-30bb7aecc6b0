/// 交易记录模型
class TransactionRecord {
  /// 交易ID
  final String id;
  
  /// 交易金额（正数为收入，负数为支出）
  final int amount;
  
  /// 交易类型
  final TransactionType type;
  
  /// 交易描述
  final String description;
  
  /// 交易时间
  final DateTime timestamp;
  
  /// 交易后余额
  final int? balanceAfter;
  
  /// 预留扩展字段
  final Map<String, dynamic>? metadata;

  /// 构造函数
  const TransactionRecord({
    required this.id,
    required this.amount,
    required this.type,
    required this.description,
    required this.timestamp,
    this.balanceAfter,
    this.metadata,
  });

  /// 将交易记录转换为Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'amount': amount,
      'type': type.name,
      'description': description,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'balanceAfter': balanceAfter,
      'metadata': metadata,
    };
  }

  /// 从Map创建交易记录对象
  factory TransactionRecord.fromMap(Map<String, dynamic> map) {
    return TransactionRecord(
      id: map['id'] as String,
      amount: map['amount'] as int,
      type: TransactionType.fromString(map['type'] as String),
      description: map['description'] as String,
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['timestamp'] as int),
      balanceAfter: map['balanceAfter'] as int?,
      metadata: map['metadata'] as Map<String, dynamic>?,
    );
  }

  /// 创建副本
  TransactionRecord copyWith({
    String? id,
    int? amount,
    TransactionType? type,
    String? description,
    DateTime? timestamp,
    int? balanceAfter,
    Map<String, dynamic>? metadata,
  }) {
    return TransactionRecord(
      id: id ?? this.id,
      amount: amount ?? this.amount,
      type: type ?? this.type,
      description: description ?? this.description,
      timestamp: timestamp ?? this.timestamp,
      balanceAfter: balanceAfter ?? this.balanceAfter,
      metadata: metadata ?? this.metadata,
    );
  }

  /// 是否为收入
  bool get isIncome => amount > 0;

  /// 是否为支出
  bool get isExpense => amount < 0;

  /// 获取绝对金额
  int get absoluteAmount => amount.abs();

  @override
  String toString() {
    return 'TransactionRecord(id: $id, amount: $amount, type: $type, description: $description, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TransactionRecord &&
        other.id == id &&
        other.amount == amount &&
        other.type == type &&
        other.description == description &&
        other.timestamp == timestamp;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        amount.hashCode ^
        type.hashCode ^
        description.hashCode ^
        timestamp.hashCode;
  }
}

/// 交易类型枚举
enum TransactionType {
  /// 收入
  earn('earn'),
  /// 支出
  spend('spend'),
  /// 系统赠送
  gift('gift'),
  /// 退款
  refund('refund');

  const TransactionType(this.value);
  
  final String value;

  /// 从字符串创建交易类型
  static TransactionType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'earn':
        return TransactionType.earn;
      case 'spend':
        return TransactionType.spend;
      case 'gift':
        return TransactionType.gift;
      case 'refund':
        return TransactionType.refund;
      default:
        throw ArgumentError('Unknown transaction type: $value');
    }
  }

  /// 获取显示名称
  String get displayName {
    switch (this) {
      case TransactionType.earn:
        return '收入';
      case TransactionType.spend:
        return '支出';
      case TransactionType.gift:
        return '赠送';
      case TransactionType.refund:
        return '退款';
    }
  }

  /// 获取图标
  String get icon {
    switch (this) {
      case TransactionType.earn:
        return '💰';
      case TransactionType.spend:
        return '💸';
      case TransactionType.gift:
        return '🎁';
      case TransactionType.refund:
        return '↩️';
    }
  }
}
