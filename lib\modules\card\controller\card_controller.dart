import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/constants/error_codes.dart';
import '../model/card.dart' as card_model;
import '../model/card_rarity.dart';
import '../model/gacha_result.dart';
import '../service/card_service.dart';

/// Card controller
class CardController extends GetxController {
  /// Card service
  final CardService _cardService = Get.find<CardService>();
  
  /// Current selected filter
  final Rx<CardRarity?> selectedRarity = Rx<CardRarity?>(null);
  
  /// Search query
  final RxString searchQuery = ''.obs;
  
  /// Sort order
  final RxString sortOrder = 'rarity'.obs; // rarity, name, date
  
  CardController();

  @override
  void onInit() {
    super.onInit();
    LogUtil.debug('CardController: 初始化');
  }

  /// Get filtered and sorted user cards
  List<card_model.Card> get filteredUserCards {
    var cards = _cardService.userCards.toList();

    // Apply rarity filter
    if (selectedRarity.value != null) {
      cards = cards.where((card) => card.rarity == selectedRarity.value).toList();
    }

    // Apply search filter
    if (searchQuery.value.isNotEmpty) {
      final query = searchQuery.value.toLowerCase();
      cards = cards.where((card) =>
        card.name.toLowerCase().contains(query) ||
        card.roleName.toLowerCase().contains(query) ||
        card.description.toLowerCase().contains(query)
      ).toList();
    }

    // Apply sorting
    switch (sortOrder.value) {
      case 'rarity':
        cards.sort((a, b) => b.rarity.stars.compareTo(a.rarity.stars));
        break;
      case 'name':
        cards.sort((a, b) => a.name.compareTo(b.name));
        break;
      case 'date':
        cards.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
    }

    return cards;
  }

  /// Get user cards
  List<card_model.Card> get userCards => _cardService.userCards;

  /// Get available cards
  List<card_model.Card> get availableCards => _cardService.availableCards;
  
  /// Get loading state
  bool get isLoading => _cardService.isLoading.value;
  
  /// Get current balance
  int get currentBalance => _cardService.currentBalance;
  
  /// Check if can afford single pull
  bool get canAffordSinglePull => _cardService.canAffordSinglePull;
  
  /// Check if can afford ten pull
  bool get canAffordTenPull => _cardService.canAffordTenPull;
  
  /// Get single pull cost
  int get singlePullCost => CardService.singlePullCost;
  
  /// Get ten pull cost
  int get tenPullCost => CardService.tenPullCost;
  
  /// Refresh user cards
  Future<void> refreshUserCards() async {
    await _cardService.refreshUserCards();
  }
  
  /// Refresh available cards
  Future<void> refreshAvailableCards() async {
    await _cardService.refreshAvailableCards();
  }
  
  /// Perform single gacha pull
  Future<GachaResult?> performSinglePull() async {
    try {
      LogUtil.debug('CardController: 执行单抽');
      return await _cardService.performSinglePull();
    } catch (e) {
      LogUtil.error('CardController: 单抽失败: $e');
      ErrorHandler.handleException(
        AppException('单抽失败', originalError: e, code: ErrorCodes.BUSINESS_ERROR),
      );
      return null;
    }
  }
  
  /// Perform ten gacha pulls
  Future<GachaResult?> performTenPull() async {
    try {
      LogUtil.debug('CardController: 执行十连抽');
      return await _cardService.performTenPull();
    } catch (e) {
      LogUtil.error('CardController: 十连抽失败: $e');
      ErrorHandler.handleException(
        AppException('十连抽失败', originalError: e, code: ErrorCodes.BUSINESS_ERROR),
      );
      return null;
    }
  }
  
  /// Set rarity filter
  void setRarityFilter(CardRarity? rarity) {
    selectedRarity.value = rarity;
  }
  
  /// Set search query
  void setSearchQuery(String query) {
    searchQuery.value = query;
  }
  
  /// Set sort order
  void setSortOrder(String order) {
    sortOrder.value = order;
  }
  
  /// Clear all filters
  void clearFilters() {
    selectedRarity.value = null;
    searchQuery.value = '';
    sortOrder.value = 'rarity';
  }
  
  /// Get card count by rarity
  int getCardCountByRarity(CardRarity rarity) {
    return userCards.where((card) => card.rarity == rarity).length;
  }
  
  /// Get total card count
  int get totalCardCount => _cardService.totalCardCount;
  
  /// Get unique card count
  int get uniqueCardCount => _cardService.uniqueCardCount;
  
  /// Format card count display
  String formatCardCount(int count) {
    if (count >= 1000) {
      return '${(count / 1000).toStringAsFixed(1)}K';
    }
    return count.toString();
  }
  
  /// Get rarity color
  Color getRarityColor(CardRarity rarity) {
    return Color(rarity.colorValue);
  }
  
  /// Get rarity display name
  String getRarityDisplayName(CardRarity rarity) {
    return rarity.displayName;
  }
  
  /// Clear user card collection
  Future<void> clearUserCards() async {
    try {
      LogUtil.debug('CardController: 清空用户卡片收藏');

      // Show confirmation dialog
      final confirmed = await Get.dialog<bool>(
        AlertDialog(
          backgroundColor: Colors.grey[900],
          title: const Text(
            'Clear Collection',
            style: TextStyle(color: Colors.white),
          ),
          content: const Text(
            'Are you sure you want to clear all your cards? This action cannot be undone.',
            style: TextStyle(color: Colors.white70),
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('Clear'),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        final success = await _cardService.clearUserCards();
        if (success) {
          ErrorHandler.showInfo('Card collection cleared');
        }
      }
    } catch (e) {
      LogUtil.error('CardController: 清空用户卡片收藏失败: $e');
      ErrorHandler.handleException(
        AppException('清空卡片收藏失败', originalError: e, code: ErrorCodes.BUSINESS_ERROR),
      );
    }
  }

  /// Create test data for development
  Future<void> createTestData() async {
    try {
      LogUtil.debug('CardController: 创建测试数据');

      final success = await _cardService.createTestData();
      if (success) {
        ErrorHandler.showInfo('Test data created successfully');
      }
    } catch (e) {
      LogUtil.error('CardController: 创建测试数据失败: $e');
      ErrorHandler.handleException(
        AppException('创建测试数据失败', originalError: e, code: ErrorCodes.BUSINESS_ERROR),
      );
    }
  }
}
